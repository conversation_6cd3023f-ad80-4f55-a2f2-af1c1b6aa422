#!/usr/bin/env python3

# 验证修复效果的脚本
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from map_handle import MapFileParser
    
    # 创建解析器实例
    parser = MapFileParser()
    
    # 测试段分类功能
    test_cases = [
        ('.text', 'text'),
        ('.text._ZN7pdm_cfg12CPdmFunction11crc16_ccittEPKhj', 'text'),
        ('.text.main', 'text'),
        ('.text.printf', 'text'),
        ('.data', 'data'),
        ('.bss', 'bss'),
        ('.rodata', 'data'),
        ('.mcal_text', 'data'),
    ]
    
    print("段分类测试结果：")
    print("=" * 60)
    
    all_passed = True
    for section_name, expected in test_cases:
        result = parser._classify_section_type(section_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} {section_name:<50} -> {result:<8} (期望: {expected})")
        if result != expected:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ 所有测试通过！段分类修复成功。")
    else:
        print("✗ 部分测试失败，需要进一步检查。")
    
    # 如果map文件存在，进行实际分析
    map_file = "int_rpu_arm8r5_arm_gcc.elf.map"
    if os.path.exists(map_file):
        print(f"\n正在分析 {map_file}...")
        parser.parse_map_file(map_file)
        
        # 获取两种分析结果
        text1, data1, bss1, other1 = parser.get_memory_usage()
        text2, data2, bss2, other2 = parser.get_module_memory_usage()
        
        print("\n分析结果对比：")
        print("-" * 60)
        print(f"{'类型':<8} {'内存占用信息':<15} {'模块占用分析':<15} {'差异':<15}")
        print("-" * 60)
        print(f"{'text':<8} {text1:<15,} {text2:<15,} {abs(text1-text2):<15,}")
        print(f"{'data':<8} {data1:<15,} {data2:<15,} {abs(data1-data2):<15,}")
        print(f"{'bss':<8} {bss1:<15,} {bss2:<15,} {abs(bss1-bss2):<15,}")
        print(f"{'other':<8} {other1:<15,} {other2:<15,} {abs(other1-other2):<15,}")
        print("-" * 60)
        total1 = text1 + data1 + bss1 + other1
        total2 = text2 + data2 + bss2 + other2
        print(f"{'总计':<8} {total1:<15,} {total2:<15,} {abs(total1-total2):<15,}")
        
        # 检查修复效果
        text_diff_percent = abs(text1 - text2) / max(text1, text2) * 100
        other_reduction = (other1 - other2) / max(other1, 1) * 100 if other1 > 0 else 0
        
        print(f"\n修复效果评估：")
        print(f"- text段差异: {text_diff_percent:.1f}%")
        print(f"- other段减少: {other_reduction:.1f}%")
        
        if text_diff_percent < 10 and other_reduction > 50:
            print("✓ 修复效果良好！")
        else:
            print("⚠ 修复效果有限，可能需要进一步调整。")
    else:
        print(f"\n⚠ Map文件 {map_file} 不存在，跳过实际分析。")

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
