#!/usr/bin/env python3

# 简单测试段分类功能
import re

def _classify_section_type(section_name):
    """
    更准确的段类型分类器 - 基于ELF标准和arm-none-eabi-size的分类逻辑
    参考ELF段属性：AX=text, WA+PROGBITS=data, WA+NOBITS=bss
    优化版本：尽量避免OTHER分类，将段归入标准三大类
    """
    section_lower = section_name.lower()
    
    # 需要跳过的段（元数据、调试信息等）
    skip_patterns = [
        'debug_', 'comment', 'note', 'gnu.', 'symtab', 
        'strtab', 'shstrtab', 'rel.', 'rela.', 'hash', 'dynsym',
        'dynstr', 'dynamic', 'interp', 'eh_frame', 'plt', 'got',
        'group', 'arm.attributes', 'stab'  # 添加stab调试段
    ]
    
    for pattern in skip_patterns:
        if pattern in section_lower:
            return 'skip'
    
    # TEXT段（可执行代码段）- 基于ELF的AX属性段
    # 这些段在ELF中具有ALLOC+EXEC属性
    text_sections = [
        '.startup_text', '.text', '.init', '.fini'
    ]
    
    # 检查精确匹配的text段
    if section_name in text_sections:
        return 'text'
    
    # 检查.text.开头的子段（这是最常见的text段格式）
    if section_name.startswith('.text.'):
        return 'text'
    
    # 检查含有code的段（通常是可执行代码）
    code_patterns = ['_code', '.code']
    for pattern in code_patterns:
        if pattern in section_lower and 'mcal_text' not in section_lower:
            return 'text'
    
    # BSS段（未初始化数据段）- 基于ELF的NOBITS类型
    # 这些段在ELF中具有ALLOC+WRITE属性但类型为NOBITS
    # 包括所有STACK段，因为它们是运行时分配的未初始化内存
    bss_patterns = [
        'bss', 'stack', 'heap', 'noinit'
    ]
    
    for pattern in bss_patterns:
        if (pattern == section_lower or 
            f'.{pattern}' == section_lower or 
            section_lower.startswith(f'{pattern}.') or 
            section_lower.startswith(f'.{pattern}.') or
            section_lower.endswith(f'_{pattern}') or
            section_lower.endswith(f'.{pattern}') or
            pattern in section_lower):  # 更宽松的匹配，包括各种STACK段
            return 'bss'
    
    # DATA段（已初始化数据段）- 基于ELF的PROGBITS类型且非可执行
    # 这些段在ELF中具有ALLOC属性且类型为PROGBITS，但不可执行
    # 包括u_boot_list段，因为它们是链接时生成的初始化数据
    data_patterns = [
        'data', 'rodata', 'init_array', 'fini_array', 'ctors', 'dtors',
        'const', 'mcal_text', 'shellcommand', 'u_boot_list', 
        'mcu_shm_noncache', 'arm.exidx'
    ]
    
    for pattern in data_patterns:
        if (pattern == section_lower or 
            f'.{pattern}' == section_lower or 
            section_lower.startswith(f'{pattern}.') or 
            section_lower.startswith(f'.{pattern}.') or
            section_lower.endswith(f'_{pattern}') or
            section_lower.endswith(f'.{pattern}') or
            pattern in section_lower):  # 更宽松的匹配u_boot_list相关段
            return 'data'
    
    # 字符串常量段（通常归类为data）
    if '.str1' in section_lower or '.str2' in section_lower:
        return 'data'
    
    # 默认归类为其他（应该很少见了）
    return 'other'

# 测试段分类
test_sections = [
    '.text',  # 主text段
    '.text._ZN7pdm_cfg12CPdmFunction11crc16_ccittEPKhj',  # text子段
    '.text.main',  # text子段
    '.text.printf',  # text子段
    '.data',  # data段
    '.bss',   # bss段
    '.rodata',  # rodata段
    '.mcal_text',  # 特殊的data段
    '.debug_info',  # 调试段
    '.ARM.exidx',  # ARM异常处理段
]

print("段分类测试结果：")
print("-" * 60)

for section in test_sections:
    classification = _classify_section_type(section)
    print(f"{section:<50} -> {classification}")

print("\n修复验证：")
print("现在 .text. 开头的子段应该被正确识别为 'text' 类型")
