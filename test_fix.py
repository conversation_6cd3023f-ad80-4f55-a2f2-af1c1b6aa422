#!/usr/bin/env python3
"""
测试修复后的map文件分析功能
"""

import sys
import os
sys.path.append('src')

from map_handle import MapFileParser

def test_classification_fix():
    """测试段分类修复效果"""
    
    # 创建解析器实例
    parser = MapFileParser()
    
    # 测试一些典型的段名称
    test_sections = [
        '.text',  # 主text段
        '.text._ZN7pdm_cfg12CPdmFunction11crc16_ccittEPKhj',  # text子段
        '.text.main',  # text子段
        '.text.printf',  # text子段
        '.data',  # data段
        '.bss',   # bss段
        '.rodata',  # rodata段
        '.mcal_text',  # 特殊的data段
    ]
    
    print("段分类测试结果：")
    print("-" * 50)
    
    for section in test_sections:
        classification = parser._classify_section_type(section)
        print(f"{section:<50} -> {classification}")
    
    print("\n" + "=" * 60)

def test_map_file_analysis():
    """测试实际map文件分析"""
    
    map_file_path = "int_rpu_arm8r5_arm_gcc.elf.map"
    
    if not os.path.exists(map_file_path):
        print(f"Map文件不存在: {map_file_path}")
        return
    
    print(f"分析map文件: {map_file_path}")
    print("=" * 60)
    
    # 创建解析器并分析文件
    parser = MapFileParser()
    parser.parse_map_file(map_file_path)
    
    # 获取内存使用信息（基于主段）
    text1, data1, bss1, other1 = parser.get_memory_usage()
    print("内存占用信息（基于主段）:")
    print(f"  text:  {text1:>10,} bytes ({text1/(text1+data1+bss1+other1)*100:5.1f}%)")
    print(f"  data:  {data1:>10,} bytes ({data1/(text1+data1+bss1+other1)*100:5.1f}%)")
    print(f"  bss:   {bss1:>10,} bytes ({bss1/(text1+data1+bss1+other1)*100:5.1f}%)")
    if other1 > 0:
        print(f"  other: {other1:>10,} bytes ({other1/(text1+data1+bss1+other1)*100:5.1f}%)")
    print(f"  总计:  {text1+data1+bss1+other1:>10,} bytes")
    
    print()
    
    # 获取模块内存使用信息（基于子段）
    text2, data2, bss2, other2 = parser.get_module_memory_usage()
    print("模块占用分析（基于子段，修复后）:")
    print(f"  text:  {text2:>10,} bytes ({text2/(text2+data2+bss2+other2)*100:5.1f}%)")
    print(f"  data:  {data2:>10,} bytes ({data2/(text2+data2+bss2+other2)*100:5.1f}%)")
    print(f"  bss:   {bss2:>10,} bytes ({bss2/(text2+data2+bss2+other2)*100:5.1f}%)")
    if other2 > 0:
        print(f"  other: {other2:>10,} bytes ({other2/(text2+data2+bss2+other2)*100:5.1f}%)")
    print(f"  总计:  {text2+data2+bss2+other2:>10,} bytes")
    
    print()
    print("差异分析:")
    print(f"  text差异:  {abs(text1-text2):>10,} bytes")
    print(f"  data差异:  {abs(data1-data2):>10,} bytes")
    print(f"  bss差异:   {abs(bss1-bss2):>10,} bytes")
    print(f"  other差异: {abs(other1-other2):>10,} bytes")
    print(f"  总计差异:  {abs((text1+data1+bss1+other1)-(text2+data2+bss2+other2)):>10,} bytes")

if __name__ == "__main__":
    print("测试map文件分析修复效果")
    print("=" * 60)
    
    # 测试段分类
    test_classification_fix()
    
    # 测试实际文件分析
    test_map_file_analysis()
